import discord
from discord.ext import commands
from discord import app_commands
from typing import Optional, List

class HelpView(discord.ui.View):
    """Interactive view for the help command with category navigation."""
    
    def __init__(self, bot: commands.Bo<PERSON>, user: discord.User):
        super().__init__(timeout=300)
        self.bot = bot
        self.user = user
        self.current_category = "overview"
        
        # Define command categories
        self.categories = {
            "overview": {
                "name": "📋 Overview",
                "description": "General bot information and quick start guide",
                "emoji": "📋"
            },
            "moderation": {
                "name": "🛡️ AI Moderation", 
                "description": "AI-powered moderation commands",
                "emoji": "🛡️"
            },
            "logging": {
                "name": "📝 Logging",
                "description": "Server event logging and moderation logs", 
                "emoji": "📝"
            },
            "security": {
                "name": "🔒 Security",
                "description": "Bot detection, raid defense, and security features",
                "emoji": "🔒"
            },
            "system": {
                "name": "⚙️ System",
                "description": "System information and bot management",
                "emoji": "⚙️"
            },
            "utility": {
                "name": "🔧 Utility", 
                "description": "User information and general utility commands",
                "emoji": "🔧"
            },
            "admin": {
                "name": "👑 Admin",
                "description": "Administrative and owner-only commands",
                "emoji": "👑"
            }
        }
        
        self.setup_dropdown()
    
    def setup_dropdown(self):
        """Setup the category selection dropdown."""
        options = []
        for key, category in self.categories.items():
            options.append(discord.SelectOption(
                label=category["name"],
                description=category["description"],
                emoji=category["emoji"],
                value=key,
                default=(key == self.current_category)
            ))
        
        select = CategorySelect(options, self)
        self.clear_items()
        self.add_item(select)

        # Add refresh button
        refresh_button = RefreshButton()
        self.add_item(refresh_button)
    
    async def interaction_check(self, interaction: discord.Interaction) -> bool:
        """Ensure only the command user can interact with the view."""
        if interaction.user.id != self.user.id:
            await interaction.response.send_message(
                "❌ You can't interact with this help menu. Use `o!help` to get your own!",
                ephemeral=True
            )
            return False
        return True
    
    async def update_category(self, interaction: discord.Interaction, category: str):
        """Update the displayed category."""
        self.current_category = category
        embed = await self.create_category_embed(category)
        self.setup_dropdown()
        await interaction.response.edit_message(embed=embed, view=self)

    async def on_timeout(self):
        """Handle view timeout."""
        # Disable all items
        for item in self.children:
            item.disabled = True

        # Try to edit the message to show it's timed out
        if hasattr(self, 'message') and self.message:
            try:
                embed = discord.Embed(
                    title="⏰ Help Menu Timed Out",
                    description="This help menu has timed out. Use `o!help` to get a new one!",
                    color=discord.Color.greyple()
                )
                await self.message.edit(embed=embed, view=self)
            except discord.NotFound:
                pass  # Message was deleted
            except discord.Forbidden:
                pass  # No permission to edit
    
    async def create_category_embed(self, category: str) -> discord.Embed:
        """Create an embed for the specified category."""
        if category == "overview":
            return await self.create_overview_embed()
        elif category == "moderation":
            return await self.create_moderation_embed()
        elif category == "logging":
            return await self.create_logging_embed()
        elif category == "security":
            return await self.create_security_embed()
        elif category == "system":
            return await self.create_system_embed()
        elif category == "utility":
            return await self.create_utility_embed()
        elif category == "admin":
            return await self.create_admin_embed()
        else:
            return await self.create_overview_embed()
    
    async def create_overview_embed(self) -> discord.Embed:
        """Create the overview embed."""
        embed = discord.Embed(
            title="🤖 AI Moderation Bot - Help",
            description="Welcome to the AI Moderation Bot! This bot provides advanced AI-powered moderation, comprehensive logging, and security features for your Discord server.",
            color=discord.Color.blue()
        )
        
        embed.add_field(
            name="🚀 Quick Start",
            value=(
                "• Use the dropdown below to browse command categories\n"
                "• Most commands are slash commands (start with `/`)\n"
                "• Some admin commands use prefix `o!`\n"
                "• Use `o!help <command>` for detailed command help"
            ),
            inline=False
        )
        
        embed.add_field(
            name="📊 Bot Statistics",
            value=(
                f"• Servers: {len(self.bot.guilds)}\n"
                f"• Commands: {len([cmd for cmd in self.bot.tree.walk_commands()])+ len(self.bot.commands)}\n"
                f"• Prefix: `o!`"
            ),
            inline=True
        )
        
        embed.add_field(
            name="🔗 Key Features",
            value=(
                "• AI-powered content moderation\n"
                "• Comprehensive event logging\n"
                "• Bot/raid detection\n"
                "• User information tools"
            ),
            inline=True
        )
        
        embed.add_field(
            name="💡 Need Help?",
            value=(
                "• Browse categories using the dropdown\n"
                "• Check command descriptions for usage\n"
                "• Admin permissions required for most config commands"
            ),
            inline=False
        )
        
        embed.set_footer(text="Select a category from the dropdown to view specific commands")
        return embed

    async def create_moderation_embed(self) -> discord.Embed:
        """Create the AI moderation commands embed."""
        embed = discord.Embed(
            title="🛡️ AI Moderation Commands",
            description="Advanced AI-powered content moderation system with configurable settings.",
            color=discord.Color.red()
        )

        embed.add_field(
            name="⚙️ Configuration Commands",
            value=(
                "`/aimod config enable` - Enable/disable AI moderation\n"
                "`/aimod config setchannel` - Set moderation log channel\n"
                "`/aimod config nsfw` - Configure NSFW channel settings\n"
                "`/aimod config language` - Set server language\n"
                "`/aimod config timeout` - Set timeout duration"
            ),
            inline=False
        )

        embed.add_field(
            name="🤖 Model Management",
            value=(
                "`/aimod model set` - Change AI model\n"
                "`/aimod model get` - View current AI model"
            ),
            inline=False
        )

        embed.add_field(
            name="📊 User Management",
            value=(
                "`/aimod infractions view` - View user infractions\n"
                "`/aimod infractions clear` - Clear user infractions\n"
                "`/aimod globalban add` - Add global ban\n"
                "`/aimod globalban remove` - Remove global ban"
            ),
            inline=False
        )

        embed.add_field(
            name="🔧 Debug & Testing",
            value=(
                "`/aimod debug testmode` - Enable test mode\n"
                "`/aimod debug last_decisions` - View recent decisions\n"
                "`/aimod appeals appeal` - Submit moderation appeal"
            ),
            inline=False
        )

        embed.add_field(
            name="📋 Other Commands",
            value=(
                "`/aimod stats` - View bot statistics\n"
                "`/aimod testlog` - Send test log embed\n"
                "`/pull_rules` - Update moderation rules"
            ),
            inline=False
        )

        embed.set_footer(text="💡 Most commands require Administrator permissions")
        return embed

    async def create_logging_embed(self) -> discord.Embed:
        """Create the logging commands embed."""
        embed = discord.Embed(
            title="📝 Logging Commands",
            description="Comprehensive server event logging and moderation log management.",
            color=discord.Color.green()
        )

        embed.add_field(
            name="🔧 Event Logging",
            value=(
                "`/log setchannel` - Set logging webhook channel\n"
                "`/log toggle` - Toggle specific event logging\n"
                "`/log status` - View all event logging status\n"
                "`/log list_keys` - List all available event keys"
            ),
            inline=False
        )

        embed.add_field(
            name="📋 Moderation Logs",
            value=(
                "`/modlog setchannel` - Set moderation log channel\n"
                "`/modlog view` - View moderation logs\n"
                "`/modlog case` - View specific case details\n"
                "`/modlog reason` - Update case reason"
            ),
            inline=False
        )

        embed.add_field(
            name="📊 Logged Events",
            value=(
                "• Member joins/leaves • Message edits/deletes\n"
                "• Role changes • Channel updates\n"
                "• Voice activity • Moderation actions\n"
                "• And many more server events!"
            ),
            inline=False
        )

        embed.set_footer(text="💡 Logging commands require Administrator permissions")
        return embed

    async def create_security_embed(self) -> discord.Embed:
        """Create the security commands embed."""
        embed = discord.Embed(
            title="🔒 Security Commands",
            description="Bot detection, raid defense, and server security features.",
            color=discord.Color.orange()
        )

        embed.add_field(
            name="🤖 Bot Detection",
            value=(
                "`/botdetect config` - Configure bot detection settings\n"
                "`/botdetect enable` - Quick enable/disable bot detection\n"
                "`/botdetect status` - View detection status\n"
                "`/botdetect defaults` - View default scam keywords"
            ),
            inline=False
        )

        embed.add_field(
            name="🛡️ Raid Defense",
            value=(
                "`/security raid config` - Configure raid defense\n"
                "• Automatic detection of mass joins\n"
                "• Owner notifications for suspicious activity\n"
                "• Quick ban tools for raid mitigation"
            ),
            inline=False
        )

        embed.add_field(
            name="⚡ Detection Features",
            value=(
                "• Scam bot keyword detection\n"
                "• Suspicious join pattern monitoring\n"
                "• Configurable action responses\n"
                "• Whitelist system for trusted users/roles"
            ),
            inline=False
        )

        embed.set_footer(text="💡 Security commands require Manage Messages or Administrator permissions")
        return embed

    async def create_system_embed(self) -> discord.Embed:
        """Create the system commands embed."""
        embed = discord.Embed(
            title="⚙️ System Commands",
            description="System information, bot management, and diagnostic tools.",
            color=discord.Color.purple()
        )

        embed.add_field(
            name="📊 System Information",
            value=(
                "`/system check` - Detailed system and bot information\n"
                "`/system temps` - View system temperature sensors\n"
                "• Hardware details • Performance metrics\n"
                "• Server count • User statistics"
            ),
            inline=False
        )

        embed.add_field(
            name="🔧 Bot Management",
            value=(
                "`o!aimod update` - Update bot from Git (Owner only)\n"
                "`o!shell <command>` - Execute shell commands (Owner only)\n"
                "`o!testerror` - Test error handling system"
            ),
            inline=False
        )

        embed.add_field(
            name="ℹ️ Information Commands",
            value=(
                "`/aimodinfo help` - View AI moderation command list\n"
                "`/aimodcredits` - View bot credits and developers"
            ),
            inline=False
        )

        embed.set_footer(text="💡 Some commands are owner-only or require special permissions")
        return embed

    async def create_utility_embed(self) -> discord.Embed:
        """Create the utility commands embed."""
        embed = discord.Embed(
            title="🔧 Utility Commands",
            description="User information tools and general utility features.",
            color=discord.Color.teal()
        )

        embed.add_field(
            name="👤 User Information",
            value=(
                "`/aboutuser` - Comprehensive user information\n"
                "• Account details • Server-specific info\n"
                "• Activity status • Custom data fields\n"
                "• Interactive navigation with buttons"
            ),
            inline=False
        )

        embed.add_field(
            name="🛠️ Admin User Tools",
            value=(
                "`/userinfo admin set` - Set custom user data (Admin)\n"
                "`/userinfo admin remove` - Remove custom data (Admin)\n"
                "• Custom field management for users\n"
                "• Administrative user data tracking"
            ),
            inline=False
        )

        embed.add_field(
            name="📋 Features",
            value=(
                "• Detailed user profiles with avatars/banners\n"
                "• Join dates and account creation info\n"
                "• Role and permission information\n"
                "• Custom administrative notes"
            ),
            inline=False
        )

        embed.set_footer(text="💡 Admin commands require Administrator permissions")
        return embed

    async def create_admin_embed(self) -> discord.Embed:
        """Create the admin commands embed."""
        embed = discord.Embed(
            title="👑 Administrative Commands",
            description="Owner-only and high-privilege administrative commands.",
            color=discord.Color.gold()
        )

        embed.add_field(
            name="🔧 Bot Management",
            value=(
                "`o!aimod update` - Update bot from Git repository\n"
                "`o!shell <command>` - Execute system shell commands\n"
                "`o!testerror` - Test the error handling system\n"
                "`/testerror` - Test slash command error handling"
            ),
            inline=False
        )

        embed.add_field(
            name="⚠️ Access Requirements",
            value=(
                "• **Bot Owner Only**: Shell, update commands\n"
                "• **Administrator**: Most configuration commands\n"
                "• **Manage Messages**: Some security features\n"
                "• **Moderate Members**: Moderation log access"
            ),
            inline=False
        )

        embed.add_field(
            name="🔒 Security Notice",
            value=(
                "⚠️ **Warning**: Admin commands can affect bot operation\n"
                "• Shell commands have full system access\n"
                "• Update commands restart the bot\n"
                "• Use with caution and proper authorization"
            ),
            inline=False
        )

        embed.set_footer(text="🚨 These commands require the highest level of permissions")
        return embed

class CategorySelect(discord.ui.Select):
    """Dropdown for selecting help categories."""

    def __init__(self, options: List[discord.SelectOption], view: HelpView):
        super().__init__(placeholder="Select a category...", options=options)
        self.help_view = view

    async def callback(self, interaction: discord.Interaction):
        await self.help_view.update_category(interaction, self.values[0])

class RefreshButton(discord.ui.Button):
    """Button to refresh the help menu."""

    def __init__(self):
        super().__init__(
            style=discord.ButtonStyle.secondary,
            emoji="🔄",
            label="Refresh"
        )

    async def callback(self, interaction: discord.Interaction):
        # Get the view from the interaction
        view = self.view
        if isinstance(view, HelpView):
            embed = await view.create_category_embed(view.current_category)
            await interaction.response.edit_message(embed=embed, view=view)

class HelpCog(commands.Cog):
    """Comprehensive help system that replaces the default discord.py help command."""
    
    def __init__(self, bot: commands.Bot):
        self.bot = bot
        # Remove the default help command
        self.bot.remove_command('help')
    
    @commands.command(name='help', aliases=['h'])
    async def help_command(self, ctx: commands.Context, *, command: Optional[str] = None):
        """
        Show help information for the bot.
        
        Usage:
        - `o!help` - Show the main help menu
        - `o!help <command>` - Show detailed help for a specific command
        """
        if command:
            await self.show_command_help(ctx, command)
        else:
            await self.show_main_help(ctx)
    
    async def show_main_help(self, ctx: commands.Context):
        """Show the main interactive help menu."""
        view = HelpView(self.bot, ctx.author)
        embed = await view.create_overview_embed()
        
        try:
            message = await ctx.send(embed=embed, view=view)
            view.message = message
        except discord.Forbidden:
            # Fallback if bot can't send embeds
            await ctx.send("❌ I need permission to send embeds to display the help menu properly.")
    
    async def show_command_help(self, ctx: commands.Context, command_name: str):
        """Show detailed help for a specific command."""
        # Try to find the command
        cmd = self.bot.get_command(command_name)
        if cmd:
            await self.show_prefix_command_help(ctx, cmd)
            return
        
        # Try to find slash command
        slash_cmd = None
        for cmd in self.bot.tree.walk_commands():
            if cmd.name == command_name or (hasattr(cmd, 'qualified_name') and cmd.qualified_name == command_name):
                slash_cmd = cmd
                break
        
        if slash_cmd:
            await self.show_slash_command_help(ctx, slash_cmd)
            return
        
        # Command not found
        embed = discord.Embed(
            title="❌ Command Not Found",
            description=f"No command named `{command_name}` was found.",
            color=discord.Color.red()
        )
        embed.add_field(
            name="💡 Suggestions",
            value=(
                "• Use `o!help` to see all available commands\n"
                "• Check your spelling\n"
                "• Some commands may be slash commands (use `/` instead of `o!`)"
            ),
            inline=False
        )
        await ctx.send(embed=embed)

    async def show_prefix_command_help(self, ctx: commands.Context, command: commands.Command):
        """Show help for a prefix command."""
        embed = discord.Embed(
            title=f"📖 Command: {command.name}",
            description=command.help or "No description available.",
            color=discord.Color.green()
        )

        # Add usage information
        usage = f"o!{command.name}"
        if command.signature:
            usage += f" {command.signature}"
        embed.add_field(name="📝 Usage", value=f"`{usage}`", inline=False)

        # Add aliases if any
        if command.aliases:
            aliases = ", ".join([f"`o!{alias}`" for alias in command.aliases])
            embed.add_field(name="🔗 Aliases", value=aliases, inline=False)

        # Add permissions if any
        if hasattr(command, 'checks') and command.checks:
            perms = []
            for check in command.checks:
                if hasattr(check, '__name__'):
                    if 'owner' in check.__name__:
                        perms.append("Bot Owner")
                    elif 'admin' in check.__name__:
                        perms.append("Administrator")
            if perms:
                embed.add_field(name="🔒 Required Permissions", value=", ".join(perms), inline=False)

        await ctx.send(embed=embed)

    async def show_slash_command_help(self, ctx: commands.Context, command: app_commands.Command):
        """Show help for a slash command."""
        embed = discord.Embed(
            title=f"📖 Slash Command: /{command.qualified_name}",
            description=command.description or "No description available.",
            color=discord.Color.green()
        )

        # Add usage information
        usage = f"/{command.qualified_name}"
        if hasattr(command, 'parameters') and command.parameters:
            params = []
            for param in command.parameters:
                if param.required:
                    params.append(f"<{param.name}>")
                else:
                    params.append(f"[{param.name}]")
            if params:
                usage += " " + " ".join(params)

        embed.add_field(name="📝 Usage", value=f"`{usage}`", inline=False)

        # Add parameter details if any
        if hasattr(command, 'parameters') and command.parameters:
            param_details = []
            for param in command.parameters:
                required = "Required" if param.required else "Optional"
                param_details.append(f"• `{param.name}` ({required}): {param.description or 'No description'}")

            if param_details:
                embed.add_field(
                    name="📋 Parameters",
                    value="\n".join(param_details[:5]),  # Limit to 5 to avoid embed limits
                    inline=False
                )

        await ctx.send(embed=embed)

async def setup(bot: commands.Bot):
    await bot.add_cog(HelpCog(bot))
